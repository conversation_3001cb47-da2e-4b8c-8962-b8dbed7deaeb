<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sidereal Astrology Chart</title>
  <link rel="stylesheet" href="sidepanel.css">
</head>
<body>
  <div class="container">
    <header>
      <h1>Sidereal Astrology</h1>
    </header>

    <!-- Layout principal en deux colonnes -->
    <div class="main-layout">
      <!-- Colonne gauche : Zodiaque -->
      <div class="zodiac-column">
        <!-- Section pour le zodiaque circulaire proportionnel -->
        <div class="circular-zodiac-section">
          <!-- Zone de boutons au-dessus du titre -->
          <div class="zodiac-header-controls">
            <button id="astrological-rules-btn" class="astrological-rules-btn">⚙️ Règles astrologiques</button>
            <button id="export-data-btn" class="export-data-btn">💾 Exporter données</button>
            <button id="import-data-btn" class="import-data-btn">📁 Importer données</button>
          </div>
          <h2>Zodiaque Circulaire Proportionnel</h2>
          <div class="circular-zodiac-description">
            <p>Visualisation circulaire où les positions zodiacales sont mappées proportionnellement (360° = 30°, 180° = 15°).</p>
          </div>

          <!-- Zone d'affichage des commentaires du jour -->
          <div class="day-comments-display" id="day-comments-display">
            <div class="comment-title">Evenements</div>
            <div class="comment-header">
              <span class="comment-date" id="comment-date">23 Mai 2025</span>
              <span class="comment-indicator" id="comment-indicator">💬</span>
            </div>
            <div class="comment-text" id="comment-text">Aucun commentaire pour ce jour</div>
          </div>

          <!-- Boutons pour afficher la légende et la carte 360° -->
          <div class="legend-button-container">
            <button id="show-legend-btn" class="legend-btn">Afficher la Légende</button>
            <button id="toggle-aspects-btn" class="aspects-toggle-btn active">🔗 Aspects</button>
            <button id="show-360-chart-btn" class="chart-360-btn">Carte du Ciel 360°</button>
            <button id="show-linear-zodiac-btn" class="linear-zodiac-btn">Zodiaque Linéaire 30°</button>
          </div>

          <!-- Contrôle de hauteur de la section -->
          <div class="height-control-container">
            <label for="circular-height-slider" class="height-control-label">📏 Hauteur :</label>
            <input type="range" id="circular-height-slider" class="height-control-slider" min="300" max="800" step="50" value="500">
            <span class="height-control-value" id="circular-height-value">500px</span>
          </div>

          <!-- Contrôle d'agrandissement du cercle -->
          <div class="circle-scale-container">
            <label for="circle-scale-slider" class="circle-scale-label">🔍 Taille du cercle :</label>
            <input type="range" id="circle-scale-slider" class="circle-scale-slider" min="50" max="150" step="5" value="100">
            <span class="circle-scale-value" id="circle-scale-value">100%</span>
            <button id="reset-circle-scale" class="reset-scale-btn" title="Remettre à 100%">↺</button>
          </div>

          <div class="circular-zodiac-container">
            <canvas id="circular-zodiac-canvas" height="500"></canvas>
          </div>

          <!-- Zodiaque linéaire 30° intégré -->
          <div id="linear-zodiac-section" class="linear-zodiac-section" style="display: none;">
            <div class="linear-zodiac-header">
              <h4 id="linear-zodiac-title">🌟 Zodiaque Linéaire 30° - Multi Transit</h4>
              <div class="linear-zodiac-controls">
                <button id="linear-360-mode-btn" class="linear-360-mode-btn" title="Basculer en mode 360°">🌍 Mode 360°</button>
                <button id="linear-circular-mode-btn" class="linear-circular-mode-btn" title="Basculer en mode circulaire">🔄 Mode Circulaire</button>
                <button id="linear-refresh-btn" class="linear-control-btn" title="Actualiser">🔄</button>
                <button id="linear-download-btn" class="linear-control-btn" title="Télécharger">💾</button>
                <button id="linear-close-btn" class="linear-control-btn" title="Fermer">✕</button>
              </div>
            </div>
            <div class="linear-zodiac-info">
              <span class="chart-birth-info" id="linear-birth-info">Naissance: 17/12/1991 07:27</span>
              <span class="chart-transit-info" id="linear-transit-info">Transit: 23/05/2025 14:30</span>
            </div>

            <!-- Section Multi-Transit -->
            <div class="linear-multi-transit-section">
              <div class="multi-transit-header">
                <h5>🌟 Multi-Transit Manager</h5>
                <button id="toggle-multi-transit-form" class="multi-transit-toggle-btn">+ Ajouter Transit</button>
              </div>

              <!-- Formulaire d'ajout de transit -->
              <div id="multi-transit-form" class="multi-transit-form" style="display: none;">
                <div class="multi-transit-inputs">
                  <div class="multi-transit-input-group">
                    <label for="multi-transit-date">Date:</label>
                    <input type="date" id="multi-transit-date" class="multi-transit-input">
                  </div>
                  <div class="multi-transit-input-group">
                    <label for="multi-transit-hour">Heure:</label>
                    <input type="number" id="multi-transit-hour" min="0" max="23" value="12" class="multi-transit-input">
                  </div>
                  <div class="multi-transit-input-group">
                    <label for="multi-transit-minute">Min:</label>
                    <input type="number" id="multi-transit-minute" min="0" max="59" value="0" class="multi-transit-input">
                  </div>
                  <div class="multi-transit-actions">
                    <button id="add-multi-transit" class="multi-transit-btn add-btn">Ajouter</button>
                    <button id="cancel-multi-transit" class="multi-transit-btn cancel-btn">Annuler</button>
                  </div>
                </div>
              </div>

              <!-- Liste des transits actifs -->
              <div id="multi-transit-list" class="multi-transit-list">
                <!-- Les transits seront affichés ici -->
              </div>
            </div>
            <div class="linear-zodiac-container-inline">
              <canvas id="linear-zodiac-canvas" width="1000" height="700"></canvas>
            </div>
          </div>
        </div>

        <!-- Section pour les aspects du zodiaque proportionnel - EN DESSOUS du zodiaque -->
        <div class="zodiac-aspects-section-below">
          <div class="aspects-section-header">
            <h4>Aspects (Mode Proportionnel 30°)</h4>
            <button id="toggle-aspects-section-btn" class="toggle-section-btn" title="Masquer/Afficher la section des aspects">
              👁️ Masquer
            </button>
          </div>
          <div id="aspects-section-content" class="aspects-section-content">
            <div class="proportional-aspects-info">
              <p><strong>Aspects proportionnels:</strong> Opposition (180°→15°), Trigone (120°→10°), Carré (90°→7.5°), Sextile (60°→5°)</p>
            </div>
            <!-- Contrôles de filtrage des aspects -->
            <div class="zodiac-aspects-filter">
            <div class="filter-row">
              <label for="proportional-orb-filter">Orb maximum:</label>
              <input type="range" id="proportional-orb-filter" min="0" max="5" step="0.1" value="1" class="orb-slider">
              <span id="proportional-orb-value" class="orb-value">1°</span>
            </div>
            <div class="filter-row">
              <div class="aspect-types-grid">
                <div class="aspect-type-item">
                  <label>
                    <input type="checkbox" name="proportional-aspect-type" value="conjunction" checked>
                    Conjonction (0°)
                  </label>
                </div>
                <div class="aspect-type-item">
                  <label>
                    <input type="checkbox" name="proportional-aspect-type" value="victoire-martial">
                    Victoire Martial (1.33°)
                  </label>
                </div>
                <div class="aspect-type-item">
                  <label>
                    <input type="checkbox" name="proportional-aspect-type" value="trigone-2">
                    Trigone 2 (3.33°)
                  </label>
                </div>
                <div class="aspect-type-item">
                  <label>
                    <input type="checkbox" name="proportional-aspect-type" value="sextile">
                    Sextile (5°)
                  </label>
                </div>
                <div class="aspect-type-item">
                  <label>
                    <input type="checkbox" name="proportional-aspect-type" value="square">
                    Carré (7.5°)
                  </label>
                </div>
                <div class="aspect-type-item">
                  <label>
                    <input type="checkbox" name="proportional-aspect-type" value="trine">
                    Trigone (10°)
                  </label>
                </div>
                <div class="aspect-type-item">
                  <label>
                    <input type="checkbox" name="proportional-aspect-type" value="trigone-3">
                    Trigone 3 (13.5°)
                  </label>
                </div>
                <div class="aspect-type-item">
                  <label>
                    <input type="checkbox" name="proportional-aspect-type" value="opposition" checked>
                    Opposition (15°)
                  </label>
                </div>
              </div>
            </div>
            <button id="proportional-apply-filter" class="apply-filter-btn">Appliquer les filtres</button>
            <button id="interpretations-btn" class="apply-filter-btn" style="margin-left: 10px; background-color: #4CAF50;">Interprétations des Aspects</button>
          </div>
            <div class="zodiac-aspects-list" id="proportional-zodiac-aspects">
              <!-- Les aspects du zodiaque proportionnel seront affichés ici -->
            </div>
          </div>
        </div>

        <!-- Section d'onglets avec tableau mensuel des aspects - EN DESSOUS des aspects -->
        <div class="monthly-aspects-section">
          <div class="monthly-aspects-header">
            <div class="monthly-aspects-tabs">
              <button class="monthly-tab-btn active" data-tab="current-month">Mois Actuel</button>
              <button class="monthly-tab-btn" data-tab="next-month">Mois Suivant</button>
              <button class="monthly-tab-btn" data-tab="prev-month">Mois Précédent</button>
            </div>
            <div class="monthly-mode-controls">
              <button id="monthly-360-mode-btn" class="monthly-360-mode-btn active">🌍 Mode 30°</button>
              <button id="monthly-transit-mode-btn" class="monthly-transit-mode-btn active">🔄 Astres Nataux</button>
            </div>
          </div>

          <div class="monthly-aspects-content">
            <div class="monthly-tab-content active" id="current-month">
              <div class="monthly-table-header">
                <h4 id="current-month-title">Aspects Transit-Natal - Décembre 2024</h4>
                <p class="monthly-table-description">Cliquez sur un jour pour voir la configuration astrologique</p>

                <!-- Section de filtres pour les aspects mensuels -->
                <div class="monthly-filters-section">
                  <div class="monthly-filters-row">
                    <div class="monthly-filter-group">
                      <label class="monthly-filter-label">Types d'aspects :</label>
                      <div class="monthly-aspect-types">
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type" value="conjunction" checked>
                          <span class="monthly-checkmark"></span>
                          Conjonction
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type" value="victoire-martial">
                          <span class="monthly-checkmark"></span>
                          Victoire Martial
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type" value="trigone-2">
                          <span class="monthly-checkmark"></span>
                          Trigone 2
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type" value="sextile">
                          <span class="monthly-checkmark"></span>
                          Sextile
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type" value="square">
                          <span class="monthly-checkmark"></span>
                          Carré
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type" value="trine">
                          <span class="monthly-checkmark"></span>
                          Trigone
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type" value="trigone-3">
                          <span class="monthly-checkmark"></span>
                          Trigone 3
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type" value="opposition" checked>
                          <span class="monthly-checkmark"></span>
                          Opposition
                        </label>
                      </div>
                    </div>

                    <div class="monthly-filter-group">
                      <label class="monthly-filter-label" for="monthly-orb-filter">Orbe :</label>
                      <div class="monthly-orb-control">
                        <input type="range" id="monthly-orb-filter" min="0.1" max="3.0" step="0.1" value="0.5">
                        <span id="monthly-orb-value">0.5°</span>
                      </div>
                    </div>

                    <div class="monthly-filter-group">
                      <button id="monthly-apply-filter" class="monthly-apply-filter-btn">Appliquer</button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="monthly-table-wrapper">
                <table class="monthly-aspects-table" id="current-month-table">
                  <thead>
                    <tr id="current-month-days-header">
                      <!-- Les jours seront générés dynamiquement -->
                    </tr>
                  </thead>
                  <tbody id="current-month-body">
                    <!-- Les lignes des planètes seront générées dynamiquement -->
                  </tbody>
                </table>
              </div>
            </div>

            <div class="monthly-tab-content" id="next-month">
              <div class="monthly-table-header">
                <h4 id="next-month-title">Aspects Transit-Natal - Janvier 2025</h4>
                <p class="monthly-table-description">Cliquez sur un jour pour voir la configuration astrologique</p>

                <!-- Section de filtres pour les aspects mensuels -->
                <div class="monthly-filters-section">
                  <div class="monthly-filters-row">
                    <div class="monthly-filter-group">
                      <label class="monthly-filter-label">Types d'aspects :</label>
                      <div class="monthly-aspect-types">
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-next" value="conjunction" checked>
                          <span class="monthly-checkmark"></span>
                          Conjonction
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-next" value="victoire-martial">
                          <span class="monthly-checkmark"></span>
                          Victoire Martial
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-next" value="trigone-2">
                          <span class="monthly-checkmark"></span>
                          Trigone 2
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-next" value="sextile">
                          <span class="monthly-checkmark"></span>
                          Sextile
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-next" value="square">
                          <span class="monthly-checkmark"></span>
                          Carré
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-next" value="trine">
                          <span class="monthly-checkmark"></span>
                          Trigone
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-next" value="trigone-3">
                          <span class="monthly-checkmark"></span>
                          Trigone 3
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-next" value="opposition" checked>
                          <span class="monthly-checkmark"></span>
                          Opposition
                        </label>
                      </div>
                    </div>

                    <div class="monthly-filter-group">
                      <label class="monthly-filter-label" for="monthly-orb-filter-next">Orbe :</label>
                      <div class="monthly-orb-control">
                        <input type="range" id="monthly-orb-filter-next" min="0.1" max="3.0" step="0.1" value="0.5">
                        <span id="monthly-orb-value-next">0.5°</span>
                      </div>
                    </div>

                    <div class="monthly-filter-group">
                      <button id="monthly-apply-filter-next" class="monthly-apply-filter-btn">Appliquer</button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="monthly-table-wrapper">
                <table class="monthly-aspects-table" id="next-month-table">
                  <thead>
                    <tr id="next-month-days-header">
                      <!-- Les jours seront générés dynamiquement -->
                    </tr>
                  </thead>
                  <tbody id="next-month-body">
                    <!-- Les lignes des planètes seront générées dynamiquement -->
                  </tbody>
                </table>
              </div>
            </div>

            <div class="monthly-tab-content" id="prev-month">
              <div class="monthly-table-header">
                <h4 id="prev-month-title">Aspects Transit-Natal - Novembre 2024</h4>
                <p class="monthly-table-description">Cliquez sur un jour pour voir la configuration astrologique</p>

                <!-- Section de filtres pour les aspects mensuels -->
                <div class="monthly-filters-section">
                  <div class="monthly-filters-row">
                    <div class="monthly-filter-group">
                      <label class="monthly-filter-label">Types d'aspects :</label>
                      <div class="monthly-aspect-types">
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-prev" value="conjunction" checked>
                          <span class="monthly-checkmark"></span>
                          Conjonction
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-prev" value="victoire-martial">
                          <span class="monthly-checkmark"></span>
                          Victoire Martial
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-prev" value="trigone-2">
                          <span class="monthly-checkmark"></span>
                          Trigone 2
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-prev" value="sextile">
                          <span class="monthly-checkmark"></span>
                          Sextile
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-prev" value="square">
                          <span class="monthly-checkmark"></span>
                          Carré
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-prev" value="trine">
                          <span class="monthly-checkmark"></span>
                          Trigone
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-prev" value="trigone-3">
                          <span class="monthly-checkmark"></span>
                          Trigone 3
                        </label>
                        <label class="monthly-checkbox-label">
                          <input type="checkbox" name="monthly-aspect-type-prev" value="opposition" checked>
                          <span class="monthly-checkmark"></span>
                          Opposition
                        </label>
                      </div>
                    </div>

                    <div class="monthly-filter-group">
                      <label class="monthly-filter-label" for="monthly-orb-filter-prev">Orbe :</label>
                      <div class="monthly-orb-control">
                        <input type="range" id="monthly-orb-filter-prev" min="0.1" max="3.0" step="0.1" value="0.5">
                        <span id="monthly-orb-value-prev">0.5°</span>
                      </div>
                    </div>

                    <div class="monthly-filter-group">
                      <button id="monthly-apply-filter-prev" class="monthly-apply-filter-btn">Appliquer</button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="monthly-table-wrapper">
                <table class="monthly-aspects-table" id="prev-month-table">
                  <thead>
                    <tr id="prev-month-days-header">
                      <!-- Les jours seront générés dynamiquement -->
                    </tr>
                  </thead>
                  <tbody id="prev-month-body">
                    <!-- Les lignes des planètes seront générées dynamiquement -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Colonne droite : Champs de saisie -->
      <div class="controls-column">
        <div class="date-inputs">
      <div class="date-time-group">
        <div class="input-group">
          <label for="birth-date">Birth Date</label>
          <input type="date" id="birth-date" name="birth-date">
        </div>
        <div class="time-inputs">
          <div class="time-input-group">
            <label for="birth-hour">Hour</label>
            <input type="number" id="birth-hour" name="birth-hour" min="0" max="23" value="12">
          </div>
          <div class="time-input-group">
            <label for="birth-minute">Min</label>
            <input type="number" id="birth-minute" name="birth-minute" min="0" max="59" value="0">
          </div>
        </div>
      </div>

      <div class="date-time-group">
        <div class="input-group">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
            <label for="transit-date">Transit Date <span class="auto-update-label" id="transit-mode-label">(Auto-updated)</span></label>
            <button id="toggle-transit-mode" class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">Mode Manuel</button>
          </div>
          <input type="date" id="transit-date" name="transit-date" class="auto-updated-field">
        </div>
        <div class="time-inputs">
          <div class="time-input-group">
            <label for="transit-hour">Hour <span class="auto-update-label">(Auto)</span></label>
            <input type="number" id="transit-hour" name="transit-hour" min="0" max="23" value="12" class="auto-updated-field">
          </div>
          <div class="time-input-group">
            <label for="transit-minute">Min <span class="auto-update-label">(Auto)</span></label>
            <input type="number" id="transit-minute" name="transit-minute" min="0" max="59" value="0" class="auto-updated-field">
          </div>
          <div class="time-input-group">
            <label for="transit-second">Sec <span class="auto-update-label">(Auto)</span></label>
            <input type="number" id="transit-second" name="transit-second" min="0" max="59" value="0" class="auto-updated-field">
          </div>
        </div>
      </div>

      <!-- Time Navigation Box - Minimal -->
      <div class="time-navigation-box" id="time-navigation-box" style="display: none;">
        <div class="time-nav-minimal">
          <div class="time-nav-group">
            <span class="time-nav-label">Jours:</span>
            <button class="time-nav-btn-mini" data-unit="day" data-value="-1">-1</button>
            <button class="time-nav-btn-mini" data-unit="day" data-value="1">+1</button>
          </div>
          <div class="time-nav-group">
            <span class="time-nav-label">Heures:</span>
            <button class="time-nav-btn-mini" data-unit="hour" data-value="-1">-1</button>
            <button class="time-nav-btn-mini" data-unit="hour" data-value="1">+1</button>
          </div>
          <button id="reset-to-now-btn" class="time-nav-reset-mini">Now</button>
        </div>

        <!-- Mini calendrier minimaliste -->
        <div class="mini-calendar-container">
          <div class="mini-calendar-header">
            <button class="mini-calendar-nav" id="mini-calendar-prev">‹</button>
            <span class="mini-calendar-title" id="mini-calendar-title">Mai 2025</span>
            <button class="mini-calendar-nav" id="mini-calendar-next">›</button>
          </div>
          <div class="mini-calendar-grid" id="mini-calendar-grid">
            <!-- Le calendrier sera généré dynamiquement -->
          </div>
        </div>
      </div>

      <button type="button" id="calculate-btn">Calculate Chart</button>

      <!-- Section de navigation par position planétaire -->
      <div class="planet-position-navigation" id="planet-position-navigation" style="display: none;">
        <h4 id="selected-planet-title">Navigation par Position</h4>
        <div class="planet-nav-info">
          <p id="planet-nav-description">Cliquez sur un astre de transit dans le zodiaque pour naviguer vers les dates où il sera à la même position.</p>
        </div>
        <div class="planet-nav-controls">
          <button id="prev-position-btn" class="planet-nav-btn" disabled>
            <span>←</span> Date Précédente
          </button>
          <button id="next-position-btn" class="planet-nav-btn" disabled>
            Date Suivante <span>→</span>
          </button>
        </div>
        <div class="planet-nav-details" id="planet-nav-details" style="display: none;">
          <div class="current-position-info">
            <strong>Position actuelle:</strong> <span id="current-planet-position"></span>
          </div>
        </div>

        <!-- Tableau des dates passées et futures -->
        <div class="planet-dates-table-container" id="planet-dates-table-container" style="display: none;">
          <h5>Dates où l'astre sera à la même position (Mode 30°)</h5>
          <div class="dates-table-wrapper">
            <table class="planet-dates-table" id="planet-dates-table">
              <thead>
                <tr>
                  <th>Type</th>
                  <th>Date</th>
                  <th>Heure</th>
                  <th>Position</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody id="planet-dates-tbody">
                <!-- Les dates seront ajoutées ici dynamiquement -->
              </tbody>
            </table>
          </div>
          <div class="table-loading" id="table-loading" style="display: none;">
            <p>Calcul des dates en cours...</p>
          </div>
        </div>
      </div>
    </div>










  </div>

  <!-- Modal pour les détails des aspects d'un jour -->
  <div id="daily-aspects-modal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 900px; max-height: 80vh; overflow-y: auto;">
      <div class="modal-header">
        <h3 id="daily-aspects-title">Détails des Aspects - 15 Mai 2025</h3>
        <span class="close" id="close-daily-aspects">&times;</span>
      </div>
      <div class="modal-body">
        <div id="daily-aspects-content">
          <div class="daily-aspects-info">
            <p id="daily-aspects-description">Aspects transit-natal pour <strong id="selected-planet-name">Soleil</strong> le <strong id="selected-date">15/05/2025</strong></p>
          </div>
          <div class="daily-aspects-table-wrapper">
            <table class="daily-aspects-table">
              <thead>
                <tr>
                  <th>Astre de Transit</th>
                  <th>Position</th>
                  <th>Aspect</th>
                  <th>Orbe</th>
                  <th>Astre Natal</th>
                  <th>Position</th>
                </tr>
              </thead>
              <tbody id="daily-aspects-tbody">
                <!-- Les aspects seront ajoutés ici dynamiquement -->
              </tbody>
            </table>
          </div>
          <div id="no-aspects-message" style="display: none;">
            <p>Aucun aspect trouvé pour cette planète à cette date.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal pour les interprétations des aspects -->
  <div id="interpretations-modal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 800px; max-height: 80vh; overflow-y: auto;">
      <div class="modal-header">
        <h3>Interprétations des Aspects Transit-Natal</h3>
        <span class="close" id="close-interpretations">&times;</span>
      </div>
      <div class="modal-body">
        <div id="interpretations-content">
          <!-- Le contenu des interprétations sera chargé ici -->
        </div>
      </div>
    </div>
  </div>

  <!-- Modal pour la légende -->
  <div id="legend-modal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 600px;">
      <div class="modal-header">
        <h3>Légende du Zodiaque</h3>
        <span class="close" id="close-legend">&times;</span>
      </div>
      <div class="modal-body">
        <div class="circular-zodiac-legend">
          <div class="legend-row">
            <div class="legend-item">
              <span class="legend-line asc-line"></span>
              <span class="legend-text">ASC</span>
            </div>
            <div class="legend-item">
              <span class="legend-line desc-line"></span>
              <span class="legend-text">DESC</span>
            </div>
            <div class="legend-item">
              <span class="legend-line mc-line"></span>
              <span class="legend-text">MC</span>
            </div>
            <div class="legend-item">
              <span class="legend-line ic-line"></span>
              <span class="legend-text">IC</span>
            </div>
            <div class="legend-item">
              <span class="legend-line asc-t-line"></span>
              <span class="legend-text">ASC-T</span>
            </div>
            <div class="legend-item">
              <span class="legend-line desc-t-line"></span>
              <span class="legend-text">DESC-T</span>
            </div>
          </div>
          <div class="legend-row">
            <div class="legend-item">
              <span class="legend-circle natal-circle"></span>
              <span class="legend-text">Natal (cercle intérieur)</span>
            </div>
            <div class="legend-item">
              <span class="legend-circle transit-circle"></span>
              <span class="legend-text">Transit (cercle extérieur)</span>
            </div>
          </div>

          <!-- Section des aspects 360° -->
          <div class="legend-section">
            <h4>Aspects Mode 360°</h4>
            <div class="legend-row">
              <div class="legend-item">
                <span class="legend-line" style="background-color: #FF0000; height: 2px;"></span>
                <span class="legend-text">Conjonction (0°) - Orb: 2°</span>
              </div>
              <div class="legend-item">
                <span class="legend-line" style="background-color: #FF8C00; height: 1px;"></span>
                <span class="legend-text">Victoire Martial (16°) - Orb: 2°</span>
              </div>
              <div class="legend-item">
                <span class="legend-line" style="background-color: #32CD32; height: 1px;"></span>
                <span class="legend-text">Trigone 2 (40°) - Orb: 2°</span>
              </div>
              <div class="legend-item">
                <span class="legend-line" style="background-color: #00AA00; height: 1px;"></span>
                <span class="legend-text">Sextile (60°) - Orb: 2°</span>
              </div>
            </div>
            <div class="legend-row">
              <div class="legend-item">
                <span class="legend-line" style="background-color: #FF6600; height: 1px;"></span>
                <span class="legend-text">Carré (90°) - Orb: 2°</span>
              </div>
              <div class="legend-item">
                <span class="legend-line" style="background-color: #0066FF; height: 1px;"></span>
                <span class="legend-text">Trigone (120°) - Orb: 1°</span>
              </div>
              <div class="legend-item">
                <span class="legend-line" style="background-color: #4169E1; height: 1px;"></span>
                <span class="legend-text">Trigone 3 (162°) - Orb: 2°</span>
              </div>
              <div class="legend-item">
                <span class="legend-line" style="background-color: #000000; height: 2px;"></span>
                <span class="legend-text">Opposition (180°) - Orb: 2°</span>
              </div>
            </div>
            <div class="legend-note">
              <small style="color: #666; font-style: italic;">
                * Survolez les lignes d'aspects en mode 360° pour voir les détails<br>
                * Mode 30° : Orb par défaut 1°, Trigone 0.5°
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal pour la sélection des aspects -->
  <div id="aspects-selection-modal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 700px;">
      <div class="modal-header">
        <h3>Configuration des Aspects</h3>
        <span class="close" id="close-aspects-selection">&times;</span>
      </div>
      <div class="modal-body">
        <div class="aspects-config-container">
          <div class="mode-selector-section">
            <h4>Mode de Configuration</h4>
            <div class="mode-selector">
              <label class="mode-option">
                <input type="radio" name="config-mode" value="mode30" checked>
                <span class="mode-label">Mode 30° (Zodiaque Proportionnel)</span>
              </label>
              <label class="mode-option">
                <input type="radio" name="config-mode" value="mode360">
                <span class="mode-label">Mode 360° (Carte du Ciel)</span>
              </label>
            </div>
          </div>
          <div class="aspects-section">
            <h4>Aspects Disponibles</h4>
            <div class="aspects-grid">
              <label class="aspect-checkbox-label">
                <input type="checkbox" name="aspect-type" value="conjunction" checked>
                <span class="aspect-name">Conjonction (0°)</span>
                <span class="aspect-color" style="background-color: #FF0000; width: 20px; height: 3px; display: inline-block; margin-left: 8px;"></span>
              </label>
              <label class="aspect-checkbox-label">
                <input type="checkbox" name="aspect-type" value="victoire-martial">
                <span class="aspect-name">Victoire Martial (16°)</span>
                <span class="aspect-color" style="background-color: #FF8C00; width: 20px; height: 2px; display: inline-block; margin-left: 8px;"></span>
              </label>
              <label class="aspect-checkbox-label">
                <input type="checkbox" name="aspect-type" value="trigone-2">
                <span class="aspect-name">Trigone 2 (40°)</span>
                <span class="aspect-color" style="background-color: #32CD32; width: 20px; height: 2px; display: inline-block; margin-left: 8px;"></span>
              </label>
              <label class="aspect-checkbox-label">
                <input type="checkbox" name="aspect-type" value="sextile">
                <span class="aspect-name">Sextile (60°)</span>
                <span class="aspect-color" style="background-color: #00AA00; width: 20px; height: 2px; display: inline-block; margin-left: 8px;"></span>
              </label>
              <label class="aspect-checkbox-label">
                <input type="checkbox" name="aspect-type" value="square">
                <span class="aspect-name">Carré (90°)</span>
                <span class="aspect-color" style="background-color: #FF6600; width: 20px; height: 2px; display: inline-block; margin-left: 8px;"></span>
              </label>
              <label class="aspect-checkbox-label">
                <input type="checkbox" name="aspect-type" value="trine" checked>
                <span class="aspect-name">Trigone (120°)</span>
                <span class="aspect-color" style="background-color: #0066FF; width: 20px; height: 2px; display: inline-block; margin-left: 8px;"></span>
              </label>
              <label class="aspect-checkbox-label">
                <input type="checkbox" name="aspect-type" value="trigone-3">
                <span class="aspect-name">Trigone 3 (162°)</span>
                <span class="aspect-color" style="background-color: #4169E1; width: 20px; height: 2px; display: inline-block; margin-left: 8px;"></span>
              </label>
              <label class="aspect-checkbox-label">
                <input type="checkbox" name="aspect-type" value="opposition" checked>
                <span class="aspect-name">Opposition (180°)</span>
                <span class="aspect-color" style="background-color: #000000; width: 20px; height: 3px; display: inline-block; margin-left: 8px;"></span>
              </label>
            </div>
          </div>
          <div class="orb-section">
            <h4>Configuration de l'Orb</h4>
            <div class="orb-control">
              <label for="aspects-orb-slider">Orb maximum: <span id="aspects-orb-value">2.0°</span></label>
              <input type="range" id="aspects-orb-slider" min="0.5" max="5.0" step="0.5" value="2.0">
            </div>
          </div>
          <div class="aspects-actions">
            <button id="apply-aspects-config" class="apply-btn">Appliquer</button>
            <button id="reset-aspects-config" class="reset-btn">Réinitialiser</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Menu contextuel pour personnaliser les couleurs des jours -->
  <div id="day-color-context-menu" class="day-color-context-menu">
    <div class="context-menu-header">
      <span id="context-menu-date">Personnaliser le jour</span>
    </div>
    <div class="context-menu-content">
      <div class="color-section">
        <label for="background-color-picker">Couleur de fond :</label>
        <div class="color-picker-container">
          <input type="color" id="background-color-picker" class="color-picker" value="#ffffff">
          <button id="reset-background-color" class="reset-color-button" title="Couleur par défaut">↺</button>
          <div class="color-preview" id="background-color-preview"></div>
        </div>
      </div>
      <div class="color-section">
        <label for="text-color-picker">Couleur du texte :</label>
        <div class="color-picker-container">
          <input type="color" id="text-color-picker" class="color-picker" value="#000000">
          <button id="reset-text-color" class="reset-color-button" title="Couleur par défaut">↺</button>
          <div class="color-preview" id="text-color-preview">A</div>
        </div>
      </div>
      <div class="color-section">
        <label for="border-color-picker">Couleur de bordure :</label>
        <div class="color-picker-container">
          <input type="color" id="border-color-picker" class="color-picker" value="#000000">
          <button id="reset-border-color" class="reset-color-button" title="Pas de bordure">↺</button>
          <div class="color-preview" id="border-color-preview" style="border: 3px solid #000000;"></div>
        </div>
      </div>
      <div class="border-size-section">
        <label for="border-size-slider">Taille de bordure :</label>
        <div class="border-size-container">
          <input type="range" id="border-size-slider" class="border-size-slider" min="1" max="8" step="1" value="2">
          <span class="border-size-value" id="border-size-value">2px</span>
          <div class="border-size-preview" id="border-size-preview"></div>
        </div>
      </div>
      <div class="icons-section">
        <label>Importance de l'événement :</label>
        <div class="icon-selector">
          <div class="icon-option" data-importance="none" title="Aucune">-</div>
          <div class="icon-option" data-importance="low" title="1 étoile">⭐</div>
          <div class="icon-option" data-importance="medium" title="2 étoiles">⭐⭐</div>
          <div class="icon-option" data-importance="high" title="3 étoiles">⭐⭐⭐</div>
        </div>
      </div>
      <div class="icons-section">
        <label>Degré de surprise :</label>
        <div class="icon-selector">
          <div class="icon-option" data-surprise="none" title="Aucune">-</div>
          <div class="icon-option" data-surprise="low" title="1 surprise">😮</div>
          <div class="icon-option" data-surprise="medium" title="2 surprises">😮😮</div>
          <div class="icon-option" data-surprise="high" title="3 surprises">😮😮😮</div>
        </div>
      </div>
      <div class="color-section">
        <label for="day-comment">Commentaire :</label>
        <textarea id="day-comment" class="day-comment-input" placeholder="Ajoutez un commentaire pour ce jour..." maxlength="200"></textarea>
        <div class="comment-counter">
          <span id="comment-char-count">0</span>/200 caractères
        </div>
      </div>
      <div class="context-menu-actions">
        <button id="apply-day-color" class="apply-color-btn">Appliquer</button>
        <button id="reset-day-color" class="reset-color-btn">Réinitialiser</button>
        <button id="cancel-day-color" class="cancel-color-btn">Annuler</button>
      </div>
    </div>
  </div>

  <!-- Modal pour les règles astrologiques -->
  <div id="astrological-rules-modal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 900px; max-height: 90vh; overflow-y: auto;">
      <div class="modal-header">
        <h3>⚙️ Règles Astrologiques Dynamiques</h3>
        <span class="close" id="close-astrological-rules">&times;</span>
      </div>
      <div class="modal-body">
        <div class="rules-intro">
          <p>Créez des règles personnalisées pour rendre le zodiaque interactif et dynamique. Les effets s'appliquent automatiquement selon les positions des planètes de transit.</p>
        </div>

        <!-- Liste des règles existantes -->
        <div class="existing-rules-section">
          <h4>Règles Actives</h4>
          <div id="rules-list" class="rules-list">
            <!-- Les règles seront affichées ici -->
          </div>
        </div>

        <!-- Formulaire de création de nouvelle règle -->
        <div class="new-rule-section">
          <h4>Créer une Nouvelle Règle</h4>
          <div class="rule-form">
            <div class="rule-condition">
              <h5>Conditions</h5>

              <!-- Type de condition -->
              <div class="condition-type-row">
                <label>Type de condition :</label>
                <select id="condition-type" class="rule-select">
                  <option value="position">Position dans un secteur</option>
                  <option value="aspect">Aspect entre planètes</option>
                  <option value="sector-coloring">Colorisation des secteurs</option>
                </select>
              </div>

              <!-- Conditions de position -->
              <div id="position-conditions" class="conditions-container">
                <div class="condition-group" data-condition-index="0">
                  <div class="condition-row">
                    <label>Quand la planète de transit</label>
                    <select class="rule-planet rule-select">
                      <option value="sun">Soleil</option>
                      <option value="moon">Lune</option>
                      <option value="mercury">Mercure</option>
                      <option value="venus">Vénus</option>
                      <option value="mars">Mars</option>
                      <option value="jupiter">Jupiter</option>
                      <option value="saturn">Saturne</option>
                      <option value="uranus">Uranus</option>
                      <option value="neptune">Neptune</option>
                      <option value="pluto">Pluton</option>
                    </select>
                    <label>est dans le secteur</label>
                    <select class="rule-sector rule-select">
                      <option value="1">Secteur 1 : 0° - 2.5°</option>
                      <option value="2">Secteur 2 : 2.5° - 5°</option>
                      <option value="3">Secteur 3 : 5° - 7.5°</option>
                      <option value="4">Secteur 4 : 7.5° - 10°</option>
                      <option value="5">Secteur 5 : 10° - 12.5°</option>
                      <option value="6">Secteur 6 : 12.5° - 15°</option>
                      <option value="7">Secteur 7 : 15° - 17.5°</option>
                      <option value="8">Secteur 8 : 17.5° - 20°</option>
                      <option value="9">Secteur 9 : 20° - 22.5°</option>
                      <option value="10">Secteur 10 : 22.5° - 25°</option>
                      <option value="11">Secteur 11 : 25° - 27.5°</option>
                      <option value="12">Secteur 12 : 27.5° - 30°</option>
                    </select>
                    <button type="button" class="remove-condition-btn" style="display: none;">✕</button>
                  </div>
                </div>
                <button type="button" id="add-position-condition" class="add-condition-btn">+ Ajouter condition ET</button>
              </div>

              <!-- Conditions d'aspects -->
              <div id="aspect-conditions" class="conditions-container" style="display: none;">
                <div class="condition-group" data-condition-index="0">
                  <div class="condition-row">
                    <label>Quand</label>
                    <select class="aspect-transit-planet rule-select">
                      <option value="sun">Soleil</option>
                      <option value="moon">Lune</option>
                      <option value="mercury">Mercure</option>
                      <option value="venus">Vénus</option>
                      <option value="mars">Mars</option>
                      <option value="jupiter">Jupiter</option>
                      <option value="saturn">Saturne</option>
                      <option value="uranus">Uranus</option>
                      <option value="neptune">Neptune</option>
                      <option value="pluto">Pluton</option>
                    </select>
                    <label>fait un</label>
                    <select class="aspect-type rule-select">
                      <option value="conjunction">Conjonction (0°)</option>
                      <option value="victoire-martial">Victoire Martial (16°)</option>
                      <option value="trigone-2">Trigone 2 (40°)</option>
                      <option value="sextile">Sextile (60°)</option>
                      <option value="square">Carré (90°)</option>
                      <option value="trine">Trigone (120°)</option>
                      <option value="trigone-3">Trigone 3 (162°)</option>
                      <option value="opposition">Opposition (180°)</option>
                    </select>
                    <label>avec</label>
                    <select class="aspect-natal-planet rule-select">
                      <option value="sun">Soleil natal</option>
                      <option value="moon">Lune natale</option>
                      <option value="mercury">Mercure natal</option>
                      <option value="venus">Vénus natale</option>
                      <option value="mars">Mars natal</option>
                      <option value="jupiter">Jupiter natal</option>
                      <option value="saturn">Saturne natal</option>
                      <option value="uranus">Uranus natal</option>
                      <option value="neptune">Neptune natal</option>
                      <option value="pluto">Pluton natal</option>
                    </select>
                    <button type="button" class="remove-condition-btn" style="display: none;">✕</button>
                  </div>
                </div>
                <button type="button" id="add-aspect-condition" class="add-condition-btn">+ Ajouter condition ET</button>
              </div>

              <!-- Conditions de colorisation des secteurs -->
              <div id="sector-coloring-conditions" class="conditions-container" style="display: none;">
                <div class="condition-group" data-condition-index="0">
                  <div class="condition-row">
                    <label>Secteur</label>
                    <select class="sector-number rule-select">
                      <option value="1">Secteur 1 : 0° - 2.5°</option>
                      <option value="2">Secteur 2 : 2.5° - 5°</option>
                      <option value="3">Secteur 3 : 5° - 7.5°</option>
                      <option value="4">Secteur 4 : 7.5° - 10°</option>
                      <option value="5">Secteur 5 : 10° - 12.5°</option>
                      <option value="6">Secteur 6 : 12.5° - 15°</option>
                      <option value="7">Secteur 7 : 15° - 17.5°</option>
                      <option value="8">Secteur 8 : 17.5° - 20°</option>
                      <option value="9">Secteur 9 : 20° - 22.5°</option>
                      <option value="10">Secteur 10 : 22.5° - 25°</option>
                      <option value="11">Secteur 11 : 25° - 27.5°</option>
                      <option value="12">Secteur 12 : 27.5° - 30°</option>
                    </select>
                    <label>=</label>
                    <select class="sector-type rule-select">
                      <option value="favorable">Favorable</option>
                      <option value="defavorable">Défavorable</option>
                      <option value="neutre">Neutre</option>
                    </select>
                    <input type="color" class="sector-color-picker" value="#00ff00" title="Couleur du secteur">
                    <button type="button" class="remove-condition-btn" style="display: none;">✕</button>
                  </div>
                </div>
                <button type="button" id="add-sector-coloring-condition" class="add-condition-btn">+ Ajouter secteur</button>
              </div>
            </div>

            <div class="rule-effects">
              <h5>Effets</h5>
              <div class="effects-grid">
                <div class="effect-group">
                  <label class="effect-checkbox">
                    <input type="checkbox" id="effect-sector-color">
                    <span class="checkmark"></span>
                    Colorier le secteur
                  </label>
                  <div class="effect-options" id="sector-color-options" style="display: none;">
                    <label>Couleur:</label>
                    <input type="color" id="sector-color-picker" value="#00ff00">
                  </div>
                </div>

                <div class="effect-group">
                  <label class="effect-checkbox">
                    <input type="checkbox" id="effect-planet-scale">
                    <span class="checkmark"></span>
                    Agrandir la planète
                  </label>
                  <div class="effect-options" id="planet-scale-options" style="display: none;">
                    <label>Facteur d'agrandissement:</label>
                    <input type="range" id="planet-scale-slider" min="1" max="5" step="0.1" value="2">
                    <span id="planet-scale-value">2x</span>
                  </div>
                </div>

                <div class="effect-group">
                  <label class="effect-checkbox">
                    <input type="checkbox" id="effect-sector-highlight">
                    <span class="checkmark"></span>
                    Surligner le secteur
                  </label>
                  <div class="effect-options" id="sector-highlight-options" style="display: none;">
                    <label>Style de surlignage:</label>
                    <select id="highlight-style">
                      <option value="glow">Lueur</option>
                      <option value="border">Bordure épaisse</option>
                      <option value="pulse">Pulsation</option>
                    </select>
                  </div>
                </div>

                <div class="effect-group">
                  <label class="effect-checkbox">
                    <input type="checkbox" id="effect-planet-glow">
                    <span class="checkmark"></span>
                    Effet de lueur sur la planète
                  </label>
                  <div class="effect-options" id="planet-glow-options" style="display: none;">
                    <label>Couleur de la lueur:</label>
                    <input type="color" id="planet-glow-color" value="#ffff00">
                  </div>
                </div>
              </div>
            </div>

            <div class="rule-actions">
              <button id="add-rule-btn" class="add-rule-btn">Ajouter la Règle</button>
              <button id="test-rule-btn" class="test-rule-btn">Tester la Règle</button>
            </div>
          </div>
        </div>

        <!-- Section de prévisualisation -->
        <div class="rule-preview-section">
          <h4>Aperçu des Effets</h4>
          <div id="rule-preview" class="rule-preview">
            <p>Sélectionnez des effets pour voir l'aperçu</p>
          </div>
        </div>
      </div>
    </div>
  </div>



  <script src="storage-migration.js"></script>
  <script src="ephemerides.js"></script>
  <script src="planets.js"></script>
  <script src="astrological-rules.js"></script>
  <script src="monthly-predictions.js"></script>
  <script src="linear-zodiac.js"></script>
  <script src="sidepanel-simple.js"></script>
</body>
</html>
